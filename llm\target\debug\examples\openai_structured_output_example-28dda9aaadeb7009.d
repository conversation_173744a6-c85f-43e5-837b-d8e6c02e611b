E:\codes\flutter\yumcha\llm\target\debug\examples\libopenai_structured_output_example-28dda9aaadeb7009.rmeta: examples\openai_structured_output_example.rs Cargo.toml

E:\codes\flutter\yumcha\llm\target\debug\examples\openai_structured_output_example-28dda9aaadeb7009.d: examples\openai_structured_output_example.rs Cargo.toml

examples\openai_structured_output_example.rs:
Cargo.toml:

# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
