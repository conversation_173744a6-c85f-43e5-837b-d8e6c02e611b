{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 3303257178207977090, "deps": [[5103565458935487, "futures_io", false, 2630800731456460246], [1615478164327904835, "pin_utils", false, 14572009987424381933], [1811549171721445101, "futures_channel", false, 3928602362869409157], [1906322745568073236, "pin_project_lite", false, 15751135382563959418], [3129130049864710036, "memchr", false, 4910561005015368720], [6955678925937229351, "slab", false, 12085895343383166142], [7013762810557009322, "futures_sink", false, 10328161094556515289], [7620660491849607393, "futures_core", false, 67156897965447383], [10565019901765856648, "futures_macro", false, 3870310147404909568], [16240732885093539806, "futures_task", false, 1252752066914117274]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-e9e9b029f315687b\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}