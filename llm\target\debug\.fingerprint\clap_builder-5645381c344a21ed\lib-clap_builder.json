{"rustc": 16591470773350601817, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 7588797288915553443, "path": 15349634706224902132, "deps": [[4858255257716900954, "anstyle", false, 207096725499288861], [11166530783118767604, "strsim", false, 15118200449054487108], [12553266436076736472, "clap_lex", false, 15990491795883670410], [13237942454122161292, "anstream", false, 9202911521591316108]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-5645381c344a21ed\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}