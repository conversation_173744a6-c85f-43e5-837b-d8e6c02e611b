{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 7588903603151011990, "deps": [[9620753569207166497, "zerovec_derive", false, 3785442482472320620], [10706449961930108323, "yoke", false, 11223195759067172893], [17046516144589451410, "zerofrom", false, 2362774189565356864]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-3437c20528ae1358\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}