{"rustc": 16591470773350601817, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 13067367211683519187, "deps": [[784494742817713399, "tower_service", false, 921410483914319120], [1906322745568073236, "pin_project_lite", false, 15751135382563959418], [2517136641825875337, "sync_wrapper", false, 13512842734903795619], [7712452662827335977, "tower_layer", false, 6534595002414969486], [7858942147296547339, "rustversion", false, 14756736440814737051], [8606274917505247608, "tracing", false, 10093633936554659411], [9010263965687315507, "http", false, 2341963323735707095], [10229185211513642314, "mime", false, 7119999706744330589], [10629569228670356391, "futures_util", false, 2619930252757155693], [11946729385090170470, "async_trait", false, 12035309347186866984], [14084095096285906100, "http_body", false, 11457077013012887498], [16066129441945555748, "bytes", false, 8184079702603890642], [16900715236047033623, "http_body_util", false, 875475143420948853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-1cb62d7b1d4b826d\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}