{"$message_type":"diagnostic","message":"you seem to be trying to use `&Box<T>`. Consider using just `&T`","code":{"code":"clippy::borrowed_box","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":5500,"byte_end":5521,"line_start":147,"line_end":147,"column_start":35,"column_end":56,"is_primary":true,"text":[{"text":"async fn run_simple_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":35,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::borrowed_box)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":5500,"byte_end":5521,"line_start":147,"line_end":147,"column_start":35,"column_end":56,"is_primary":true,"text":[{"text":"async fn run_simple_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":35,"highlight_end":56}],"label":null,"suggested_replacement":"&dyn LLMProvider","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `&Box<T>`. Consider using just `&T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:147:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn run_simple_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `&dyn LLMProvider`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::borrowed_box)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you seem to be trying to use `&Box<T>`. Consider using just `&T`","code":{"code":"clippy::borrowed_box","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":8082,"byte_end":8103,"line_start":221,"line_end":221,"column_start":39,"column_end":60,"is_primary":true,"text":[{"text":"async fn run_multi_turn_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":39,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":8082,"byte_end":8103,"line_start":221,"line_end":221,"column_start":39,"column_end":60,"is_primary":true,"text":[{"text":"async fn run_multi_turn_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":39,"highlight_end":60}],"label":null,"suggested_replacement":"&dyn LLMProvider","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `&Box<T>`. Consider using just `&T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:221:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn run_multi_turn_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `&dyn LLMProvider`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you seem to be trying to use `&Box<T>`. Consider using just `&T`","code":{"code":"clippy::borrowed_box","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":9499,"byte_end":9520,"line_start":257,"line_end":257,"column_start":40,"column_end":61,"is_primary":true,"text":[{"text":"async fn run_tool_choice_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":40,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":9499,"byte_end":9520,"line_start":257,"line_end":257,"column_start":40,"column_end":61,"is_primary":true,"text":[{"text":"async fn run_tool_choice_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {","highlight_start":40,"highlight_end":61}],"label":null,"suggested_replacement":"&dyn LLMProvider","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `&Box<T>`. Consider using just `&T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:257:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn run_tool_choice_scenario(llm: &Box<dyn LLMProvider>) -> Result<(), Box<dyn Error>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `&dyn LLMProvider`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you seem to be trying to use `&Box<T>`. Consider using just `&T`","code":{"code":"clippy::borrowed_box","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":10424,"byte_end":10445,"line_start":281,"line_end":281,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"    llm: &Box<dyn LLMProvider>,","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":10424,"byte_end":10445,"line_start":281,"line_end":281,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"    llm: &Box<dyn LLMProvider>,","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":"&dyn LLMProvider","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `&Box<T>`. Consider using just `&T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:281:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    llm: &Box<dyn LLMProvider>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `&dyn LLMProvider`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you seem to be trying to use `match` for destructuring a single pattern. Consider using `if let`","code":{"code":"clippy::single_match","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":10740,"byte_end":11142,"line_start":291,"line_end":301,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    match llm.tools() {","highlight_start":5,"highlight_end":24},{"text":"        Some(tools) => {","highlight_start":1,"highlight_end":25},{"text":"            for tool in tools {","highlight_start":1,"highlight_end":32},{"text":"                builder = builder.function(","highlight_start":1,"highlight_end":44},{"text":"                    FunctionBuilder::new(&tool.function.name)","highlight_start":1,"highlight_end":62},{"text":"                        .description(&tool.function.description), // Note: we'd need to recreate all parameters here in a real implementation","highlight_start":1,"highlight_end":142},{"text":"                );","highlight_start":1,"highlight_end":19},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"        None => {}","highlight_start":1,"highlight_end":19},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_match","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::single_match)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":10740,"byte_end":11142,"line_start":291,"line_end":301,"column_start":5,"column_end":6,"is_primary":true,"text":[{"text":"    match llm.tools() {","highlight_start":5,"highlight_end":24},{"text":"        Some(tools) => {","highlight_start":1,"highlight_end":25},{"text":"            for tool in tools {","highlight_start":1,"highlight_end":32},{"text":"                builder = builder.function(","highlight_start":1,"highlight_end":44},{"text":"                    FunctionBuilder::new(&tool.function.name)","highlight_start":1,"highlight_end":62},{"text":"                        .description(&tool.function.description), // Note: we'd need to recreate all parameters here in a real implementation","highlight_start":1,"highlight_end":142},{"text":"                );","highlight_start":1,"highlight_end":19},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"        None => {}","highlight_start":1,"highlight_end":19},{"text":"    }","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"if let Some(tools) = llm.tools() {\n        for tool in tools {\n            builder = builder.function(\n                FunctionBuilder::new(&tool.function.name)\n                    .description(&tool.function.description), // Note: we'd need to recreate all parameters here in a real implementation\n            );\n        }\n    }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `match` for destructuring a single pattern. Consider using `if let`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:291:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m    match llm.tools() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Some(tools) => {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            for tool in tools {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                builder = builder.function(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        None => {}\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m|_____^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_match\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(clippy::single_match)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: try\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[38;5;10mif let Some(tools) = llm.tools() {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+         for tool in tools {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             builder = builder.function(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                 FunctionBuilder::new(&tool.function.name)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+                     .description(&tool.function.description), // Note: we'd need to recreate all parameters here in a real implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             );\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+         }\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+     }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"you seem to be trying to use `&Box<T>`. Consider using just `&T`","code":{"code":"clippy::borrowed_box","explanation":null},"level":"warning","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":12469,"byte_end":12490,"line_start":342,"line_end":342,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"    llm: &Box<dyn LLMProvider>,","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"examples\\unified_tool_calling_example.rs","byte_start":12469,"byte_end":12490,"line_start":342,"line_end":342,"column_start":10,"column_end":31,"is_primary":true,"text":[{"text":"    llm: &Box<dyn LLMProvider>,","highlight_start":10,"highlight_end":31}],"label":null,"suggested_replacement":"&dyn LLMProvider","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: you seem to be trying to use `&Box<T>`. Consider using just `&T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\unified_tool_calling_example.rs:342:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m342\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    llm: &Box<dyn LLMProvider>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: try: `&dyn LLMProvider`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#borrowed_box\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"6 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 6 warnings emitted\u001b[0m\n\n"}
