{"rustc": 16591470773350601817, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 7588797288915553443, "path": 16691723839666053911, "deps": [[3019522439560520108, "clap_builder", false, 6319770462149493061], [17056525256108235978, "clap_derive", false, 9336433785905120730]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-b5303cd658de1d54\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}