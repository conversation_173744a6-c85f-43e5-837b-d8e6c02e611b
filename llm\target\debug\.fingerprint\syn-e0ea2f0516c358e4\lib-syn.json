{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"parsing\", \"printing\", \"proc-macro\", \"quote\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"test\", \"visit\", \"visit-mut\"]", "target": 11103975901103234717, "profile": 2225463790103693989, "path": 17364033415754090679, "deps": [[1988483478007900009, "unicode_ident", false, 18252182728405084144], [2713742371683562785, "build_script_build", false, 12226763581599702603], [3060637413840920116, "proc_macro2", false, 7961323620637496474], [17990358020177143287, "quote", false, 14167604272425793685]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-e0ea2f0516c358e4\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}