{"rustc": 16591470773350601817, "features": "[\"custom-bindings\", \"default\", \"fd-lock\", \"home\", \"radix_trie\", \"with-dirs\", \"with-file-history\"]", "declared_features": "[\"buffer-redux\", \"case_insensitive_history_search\", \"custom-bindings\", \"default\", \"derive\", \"fd-lock\", \"home\", \"radix_trie\", \"regex\", \"rusqlite\", \"rustyline-derive\", \"signal-hook\", \"skim\", \"termios\", \"with-dirs\", \"with-file-history\", \"with-fuzzy\", \"with-sqlite-history\"]", "target": 1801494506462427169, "profile": 2241668132362809309, "path": 9412941850529357122, "deps": [[1232198224951696867, "unicode_segmentation", false, 5597086941623254560], [2924422107542798392, "libc", false, 14984148004760848788], [3129130049864710036, "memchr", false, 4910561005015368720], [4544379658388519060, "home", false, 15557669108052020405], [5986029879202738730, "log", false, 881829371126356465], [6389928905734779823, "unicode_width", false, 2568136464814539025], [7896293946984509699, "bitflags", false, 3903921708477240841], [9520952519707787197, "fd_lock", false, 5469060546174572531], [10281541584571964250, "windows_sys", false, 4884731148659169617], [10411997081178400487, "cfg_if", false, 5871431143505063194], [13495677209339419690, "radix_trie", false, 3258629714515454310], [17163538222608697903, "clipboard_win", false, 402010326242958586]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustyline-fa59dc6f7f0a6adf\\dep-lib-rustyline", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}