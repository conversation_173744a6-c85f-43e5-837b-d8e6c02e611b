{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 17467636112133979524, "path": 13236182631312062850, "deps": [[5103565458935487, "futures_io", false, 2630800731456460246], [1811549171721445101, "futures_channel", false, 3928602362869409157], [7013762810557009322, "futures_sink", false, 10328161094556515289], [7620660491849607393, "futures_core", false, 67156897965447383], [10629569228670356391, "futures_util", false, 2619930252757155693], [12779779637805422465, "futures_executor", false, 4658528132966677775], [16240732885093539806, "futures_task", false, 1252752066914117274]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-d552e683db561b48\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}