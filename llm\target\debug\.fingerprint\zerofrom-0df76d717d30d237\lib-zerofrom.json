{"rustc": 16591470773350601817, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 5661795991871761775, "deps": [[4022439902832367970, "zerofrom_derive", false, 4079892237583835358]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-0df76d717d30d237\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}